"use client";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Icon from "@/components/icon";
import { useState } from "react";

interface DialogueItem {
  type: "user" | "assistant";
  content: string;
  timestamp: string;
  code?: string;
  annotations?: string[];
}

interface DialogueSection {
  name: string;
  title: string;
  label: string;
  description: string;
  items: DialogueItem[];
}

interface DialogueProps {
  section: DialogueSection;
}

export default function Dialogue({ section }: DialogueProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const handleCopyCode = (code: string, index: number) => {
    navigator.clipboard.writeText(code);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  return (
    <section id={section.name} className="relative py-16 lg:py-24">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <Badge variant="outline" className="mb-4">
            {section.label}
          </Badge>
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            {section.title}
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            {section.description}
          </p>
        </div>

        <div className="mt-12 space-y-6">
          {section.items.map((item, index) => (
            <Card
              key={index}
              className={`p-6 ${
                item.type === "user" 
                  ? "ml-0 mr-auto max-w-3xl bg-muted/30" 
                  : "ml-auto mr-0 max-w-3xl"
              }`}
            >
              <div className="flex items-start gap-4">
                <div className={`rounded-full p-2 ${
                  item.type === "user" 
                    ? "bg-primary/10 text-primary" 
                    : "bg-secondary/10 text-secondary-foreground"
                }`}>
                  {item.type === "user" ? (
                    <Icon name="RiUserLine" className="h-5 w-5" />
                  ) : (
                    <Icon name="RiRobot2Line" className="h-5 w-5" />
                  )}
                </div>
                
                <div className="flex-1 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {item.type === "user" ? "Developer" : "Claude"}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {item.timestamp}
                    </span>
                  </div>
                  
                  <p className="text-sm">{item.content}</p>
                  
                  {item.code && (
                    <div className="relative">
                      <pre className="overflow-x-auto rounded-lg bg-muted p-4 text-xs">
                        <code>{item.code}</code>
                      </pre>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="absolute right-2 top-2"
                        onClick={() => handleCopyCode(item.code!, index)}
                      >
                        <Icon name="RiFileCopyLine" className="h-4 w-4" />
                        {copiedIndex === index ? "Copied!" : "Copy"}
                      </Button>
                    </div>
                  )}
                  
                  {item.annotations && (
                    <div className="flex flex-wrap gap-2">
                      {item.annotations.map((annotation, i) => (
                        <Badge key={i} variant="secondary" className="text-xs">
                          {annotation}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        <div className="mt-8 text-center">
          <Button variant="outline">
            View Full Conversation
          </Button>
        </div>
      </div>
    </section>
  );
}