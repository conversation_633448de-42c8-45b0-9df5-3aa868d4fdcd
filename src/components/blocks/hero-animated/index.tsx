"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import Icon from "@/components/icon";
import { useEffect, useState } from "react";

interface HeroAnimatedProps {
  hero: any;
}

export default function HeroAnimated({ hero }: HeroAnimatedProps) {
  const [codeLines, setCodeLines] = useState<string[]>([]);
  const [promptText, setPromptText] = useState("");
  
  const prompts = [
    "Create a React component for user authentication",
    "Build an API endpoint with rate limiting",
    "Generate a responsive dashboard layout",
    "Implement real-time chat functionality"
  ];
  
  const codes = [
    ["const AuthComponent = () => {", "  const [user, setUser] = useState(null);", "  // Authentication logic here", "};"],
    ["app.post('/api/endpoint', rateLimiter, (req, res) => {", "  // Handle request with rate limiting", "});"],
    ["<div className='grid grid-cols-12 gap-4'>", "  <aside className='col-span-3'>...</aside>", "  <main className='col-span-9'>...</main>", "</div>"],
    ["const socket = io();", "socket.on('message', (data) => {", "  updateChat(data);", "});"]
  ];

  useEffect(() => {
    let promptIndex = 0;
    let charIndex = 0;
    let isTyping = true;
    let currentPrompt = prompts[0];
    let currentCode = codes[0];
    
    const typePrompt = () => {
      if (isTyping && charIndex < currentPrompt.length) {
        setPromptText(currentPrompt.substring(0, charIndex + 1));
        charIndex++;
      } else if (isTyping) {
        isTyping = false;
        // Start showing code
        let lineIndex = 0;
        const codeInterval = setInterval(() => {
          if (lineIndex < currentCode.length) {
            setCodeLines(currentCode.slice(0, lineIndex + 1));
            lineIndex++;
          } else {
            clearInterval(codeInterval);
            // Wait and then start next prompt
            setTimeout(() => {
              promptIndex = (promptIndex + 1) % prompts.length;
              currentPrompt = prompts[promptIndex];
              currentCode = codes[promptIndex];
              charIndex = 0;
              isTyping = true;
              setPromptText("");
              setCodeLines([]);
            }, 2000);
          }
        }, 300);
      }
    };
    
    const interval = setInterval(typePrompt, 50);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-background to-muted/30">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute left-1/4 top-1/4 h-96 w-96 rounded-full bg-primary/5 blur-3xl" />
        <div className="absolute right-1/4 bottom-1/4 h-96 w-96 rounded-full bg-secondary/5 blur-3xl" />
      </div>
      
      <div className="relative mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8 lg:py-32">
        <div className="mx-auto max-w-3xl text-center">
          {hero.announcement && (
            <a
              href={hero.announcement.url}
              className="inline-flex items-center gap-2 rounded-full bg-muted px-3 py-1 text-sm font-medium transition-colors hover:bg-muted/80"
            >
              <Badge variant="secondary" className="px-1.5 py-0.5 text-xs">
                {hero.announcement.label}
              </Badge>
              <span>{hero.announcement.title}</span>
            </a>
          )}
          
          <h1 className="mt-8 text-4xl font-bold tracking-tight sm:text-6xl">
            {hero.title.split(hero.highlight_text).map((part: string, index: number) => (
              <span key={index}>
                {part}
                {index === 0 && (
                  <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    {hero.highlight_text}
                  </span>
                )}
              </span>
            ))}
          </h1>
          
          <p
            className="mt-6 text-lg text-muted-foreground"
            dangerouslySetInnerHTML={{ __html: hero.description }}
          />
          
          {hero.tip && (
            <p className="mt-4 text-sm font-medium text-primary">{hero.tip}</p>
          )}
          
          <div className="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-center">
            {hero.buttons?.map((button: any, index: number) => (
              <Button
                key={index}
                size="lg"
                variant={button.variant || "default"}
                asChild
              >
                <a href={button.url} target={button.target}>
                  {button.icon && <Icon name={button.icon} className="mr-2 h-5 w-5" />}
                  {button.title}
                </a>
              </Button>
            ))}
          </div>
        </div>
        
        <div className="mt-16 grid gap-8 lg:grid-cols-2">
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <div className="mb-4 flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-red-500" />
              <div className="h-3 w-3 rounded-full bg-yellow-500" />
              <div className="h-3 w-3 rounded-full bg-green-500" />
              <span className="ml-2 text-sm text-muted-foreground">prompt.txt</span>
            </div>
            <div className="font-mono text-sm">
              <span className="text-muted-foreground">$</span> {promptText}
              <span className="animate-pulse">|</span>
            </div>
          </div>
          
          <div className="rounded-lg border bg-card p-6 shadow-sm">
            <div className="mb-4 flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-red-500" />
              <div className="h-3 w-3 rounded-full bg-yellow-500" />
              <div className="h-3 w-3 rounded-full bg-green-500" />
              <span className="ml-2 text-sm text-muted-foreground">output.js</span>
            </div>
            <div className="space-y-1 font-mono text-sm">
              {codeLines.map((line, index) => (
                <div key={index} className="text-green-600 dark:text-green-400">
                  {line}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}