"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";

interface LearningItem {
  level: string;
  title: string;
  description: string;
  lessons: number;
  duration: string;
  icon: string;
}

interface LearningSection {
  name: string;
  title: string;
  label: string;
  description: string;
  items: LearningItem[];
}

interface LearningProps {
  section: LearningSection;
}

export default function Learning({ section }: LearningProps) {
  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case "beginner":
      case "初级":
        return "text-green-600 bg-green-50 dark:bg-green-950";
      case "intermediate":
      case "中级":
        return "text-blue-600 bg-blue-50 dark:bg-blue-950";
      case "expert":
      case "高级":
        return "text-purple-600 bg-purple-50 dark:bg-purple-950";
      default:
        return "text-gray-600 bg-gray-50 dark:bg-gray-950";
    }
  };

  return (
    <section id={section.name} className="relative py-16 lg:py-24 bg-muted/30">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center">
          <Badge variant="outline" className="mb-4">
            {section.label}
          </Badge>
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            {section.title}
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            {section.description}
          </p>
        </div>

        <div className="mt-12 grid gap-8 lg:grid-cols-3">
          {section.items.map((item, index) => (
            <Card key={index} className="relative overflow-hidden">
              <div className={`absolute left-0 top-0 h-1 w-full ${
                index === 0 ? "bg-green-500" : index === 1 ? "bg-blue-500" : "bg-purple-500"
              }`} />
              
              <CardHeader className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge className={getLevelColor(item.level)} variant="secondary">
                    {item.level}
                  </Badge>
                  <Icon name={item.icon} className="h-8 w-8 text-muted-foreground" />
                </div>
                <CardTitle className="text-xl">{item.title}</CardTitle>
                <CardDescription>{item.description}</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    {item.lessons} lessons
                  </span>
                  <span className="font-medium">{item.duration}</span>
                </div>
                
                <Button className="w-full" variant={index === 1 ? "default" : "outline"}>
                  {index === 0 ? "Start Learning" : index === 1 ? "Continue Learning" : "Coming Soon"}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-sm text-muted-foreground">
            All courses include hands-on exercises, real project examples, and community support
          </p>
        </div>
      </div>
    </section>
  );
}