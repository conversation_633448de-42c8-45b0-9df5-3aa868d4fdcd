"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import Image from "next/image";

interface ShowcaseMetrics {
  prompts: number;
  time: string;
  likes: number;
}

interface ShowcaseItem {
  title: string;
  description: string;
  tags?: string[];
  image?: {
    src: string;
    alt?: string;
  };
  metrics?: ShowcaseMetrics;
}

interface ShowcaseSection {
  name: string;
  title: string;
  label?: string;
  description?: string;
  items: ShowcaseItem[];
}

interface ShowcaseEnhancedProps {
  section: ShowcaseSection;
}

export default function ShowcaseEnhanced({ section }: ShowcaseEnhancedProps) {
  return (
    <section id={section.name} className="relative py-16 lg:py-24">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {(section.label || section.title || section.description) && (
          <div className="mx-auto max-w-3xl text-center">
            {section.label && (
              <Badge variant="outline" className="mb-4">
                {section.label}
              </Badge>
            )}
            {section.title && (
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
                {section.title}
              </h2>
            )}
            {section.description && (
              <p className="mt-4 text-lg text-muted-foreground">
                {section.description}
              </p>
            )}
          </div>
        )}

        <div className="mt-12 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {section.items.map((item, index) => (
            <Card key={index} className="group overflow-hidden transition-all hover:shadow-lg">
              {item.image && (
                <div className="relative aspect-video overflow-hidden bg-muted">
                  <Image
                    src={item.image.src}
                    alt={item.image.alt || item.title}
                    fill
                    className="object-cover transition-transform group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity group-hover:opacity-100" />
                </div>
              )}
              
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold">{item.title}</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  {item.description}
                </p>
                
                {item.tags && (
                  <div className="mt-4 flex flex-wrap gap-2">
                    {item.tags.map((tag, tagIndex) => (
                      <Badge key={tagIndex} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
              
              {item.metrics && (
                <CardFooter className="border-t bg-muted/30 p-4">
                  <div className="flex w-full items-center justify-between text-sm">
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Icon name="RiChat3Line" className="h-4 w-4" />
                      <span>{item.metrics.prompts} prompts</span>
                    </div>
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Icon name="RiTimeLine" className="h-4 w-4" />
                      <span>{item.metrics.time}</span>
                    </div>
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Icon name="RiHeartLine" className="h-4 w-4" />
                      <span>{item.metrics.likes}</span>
                    </div>
                  </div>
                </CardFooter>
              )}
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button size="lg" variant="outline">
            View All Projects
          </Button>
        </div>
      </div>
    </section>
  );
}