import CTA from "@/components/blocks/cta";
import Dialogue from "@/components/blocks/dialogue";
import Feature from "@/components/blocks/feature";
import HeroAnimated from "@/components/blocks/hero-animated";
import Learning from "@/components/blocks/learning";
import Pricing from "@/components/blocks/pricing";
import ShowcaseEnhanced from "@/components/blocks/showcase-enhanced";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getClaudeCodePage } from "@/services/page";
import { setRequestLocale } from "next-intl/server";

export const revalidate = 60;
export const dynamic = "force-static";
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    title: "Claude Code Show - Code is Cheap, Show Me the Talk",
    description: "Explore the art of AI programming through prompts. Discover how developers craft Claude Code projects with natural language.",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  const page = await getClaudeCodePage(locale);

  return (
    <>
      {page.hero && <HeroAnimated hero={page.hero} />}
      {page.stats && <Stats section={page.stats} />}
      {page.feature && <Feature section={page.feature} />}
      {page.dialogue && <Dialogue section={page.dialogue} />}
      {page.showcase && <ShowcaseEnhanced section={page.showcase} />}
      {page.learning && <Learning section={page.learning} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
