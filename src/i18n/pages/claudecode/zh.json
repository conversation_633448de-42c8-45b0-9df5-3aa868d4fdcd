{"template": "shipany-template-one", "theme": "dark", "header": {"brand": {"title": "<PERSON>", "logo": {"src": "/logo.png", "alt": "<PERSON>"}, "url": "/"}, "nav": {"items": [{"title": "探索", "url": "/#explore", "icon": "RiCompassDiscoverLine"}, {"title": "项目展示", "url": "/#showcases", "icon": "RiApps2Line"}, {"title": "学习", "url": "/#learn", "icon": "RiBookOpenLine"}, {"title": "定价", "url": "/#pricing", "icon": "RiMoneyDollarCircleLine"}]}, "buttons": [{"title": "加入社区", "url": "#", "target": "_blank", "variant": "outline", "icon": "RiTeamLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Code is <PERSON><PERSON><PERSON>, Show Me the Talk", "highlight_text": "Show Me the Talk", "description": "探索 AI 编程背后的 Prompt 艺术。<br/>发现开发者如何用自然语言打造 Claude Code 项目。", "announcement": {"label": "新", "title": "🚀 已上线 1000+ 项目", "url": "/#showcases"}, "tip": "🎯 从真实 Prompt 中学习", "buttons": [{"title": "开始探索", "icon": "RiSearchLine", "url": "/#explore", "target": "_self", "variant": "default"}, {"title": "查看热门项目", "icon": "RiFireLine", "url": "/#showcases", "target": "_self", "variant": "outline"}], "show_happy_users": true, "show_badge": true}, "stats": {"name": "stats", "label": "影响力", "title": "改变编程方式", "description": "来自 Claude Code 社区的真实数据", "icon": "RiLineChartLine", "items": [{"title": "已生成项目数", "label": "1,234+", "description": "持续增长中"}, {"title": "节省开发时间", "label": "50K+", "description": "小时（预估）"}, {"title": "活跃工程师", "label": "500+", "description": "Prompt 大师"}]}, "feature": {"name": "feature", "title": "为什么选择 Claude Code Show？", "description": "一个展示 AI 与人类创造力在编程中交汇的平台。", "items": [{"title": "真实项目展示", "description": "浏览使用 Claude Code 构建的实际项目，包含源代码和在线演示。", "icon": "RiCodeBoxLine"}, {"title": "完整对话历史", "description": "查看开发者与 Claude 的完整对话，理解思考过程。", "icon": "RiChat3Line"}, {"title": "Prompt 模板库", "description": "发现并保存针对不同编程场景的有效 Prompt 模式。", "icon": "RiFileCopyLine"}, {"title": "学习资源", "description": "从新手到专家，跟随结构化路径掌握 AI 辅助开发。", "icon": "RiGraduationCapLine"}, {"title": "社区洞察", "description": "学习经验丰富的 Prompt 工程师分享的注解和技巧。", "icon": "RiLightbulbLine"}, {"title": "代码分析", "description": "通过 AI 驱动的分析和指标，了解什么让 Prompt 更有效。", "icon": "RiBarChartBoxLine"}]}, "dialogue": {"name": "dialogue", "title": "体验 AI 对话", "label": "互动演示", "description": "看开发者如何通过自然对话将想法转化为代码", "items": [{"type": "user", "content": "创建一个 React 待办事项列表组件，包含添加、删除和标记完成功能", "timestamp": "10:23"}, {"type": "assistant", "content": "我将创建一个现代的 React 待办事项列表组件，包含您要求的所有功能...", "timestamp": "10:23", "code": "// TodoList 组件实现\nconst TodoList = () => {\n  // 组件逻辑\n}", "annotations": ["关键洞察：从状态管理开始", "最佳实践：使用 React Hooks"]}]}, "showcase": {"name": "showcase", "label": "精选", "title": "热门 Claude Code 项目", "description": "探索通过 AI 对话构建的精彩项目", "items": [{"title": "电商平台", "description": "2小时内构建的全栈在线商店，包含支付集成", "tags": ["React", "Node.js", "Stripe"], "image": {"src": "/imgs/showcases/1.png"}, "metrics": {"prompts": 12, "time": "2小时", "likes": 234}}, {"title": "AI 聊天界面", "description": "精美的聊天 UI，支持流式响应和 Markdown", "tags": ["Next.js", "Tailwind", "AI"], "image": {"src": "/imgs/showcases/2.png"}, "metrics": {"prompts": 8, "time": "1小时", "likes": 189}}, {"title": "数据仪表板", "description": "带有交互式图表的实时分析仪表板", "tags": ["React", "D3.js", "WebSocket"], "image": {"src": "/imgs/showcases/3.png"}, "metrics": {"prompts": 15, "time": "3小时", "likes": 312}}]}, "learning": {"name": "learning", "title": "掌握 AI 编程", "label": "学习路径", "description": "从新手到 Prompt 工程专家", "items": [{"level": "初级", "title": "Claude Code 入门", "description": "学习与 AI 对话生成代码的基础知识", "lessons": 5, "duration": "2 小时", "icon": "RiSeedlingLine"}, {"level": "中级", "title": "有效的 Prompt 模式", "description": "掌握复杂项目的高级技巧", "lessons": 8, "duration": "4 小时", "icon": "RiPlantLine"}, {"level": "高级", "title": "Prompt 工程精通", "description": "使用 AI 构建生产级应用", "lessons": 12, "duration": "8 小时", "icon": "RiTreeLine"}]}, "testimonial": {"name": "testimonial", "label": "评价", "title": "开发者怎么说", "description": "听听社区对 Claude Code 的使用体验", "icon": "RiStarFill", "items": [{"title": "陈莎拉", "label": "全栈开发者", "description": "Claude Code Show 让我大开眼界，看到了 AI 编程的可能性。真实的项目示例极大地提升了我的提示技巧。", "image": {"src": "/imgs/users/1.png"}}, {"title": "迈克·约翰逊", "label": "创业公司创始人", "description": "我们在几天内而不是几个月内构建了 MVP。Claude Code Show 上的 Prompt 模板对我们的快速开发非常宝贵。", "image": {"src": "/imgs/users/2.png"}}, {"title": "艾米丽·罗德里格斯", "label": "AI 爱好者", "description": "对话历史记录太棒了！看到经验丰富的开发者如何与 Claude 互动，比任何教程都让我学到更多。", "image": {"src": "/imgs/users/3.png"}}]}, "pricing": {"name": "pricing", "title": "选择您的方案", "description": "解锁 AI 编程洞察的全部力量", "items": [{"title": "免费版", "price": "¥0", "period": "永久", "description": "适合探索体验", "features": ["浏览公开项目", "查看基础对话", "每日使用限额", "社区支持"], "button": {"title": "开始使用", "url": "#"}}, {"title": "专业版", "price": "¥69", "period": "/月", "description": "适合专业开发者", "popular": true, "features": ["无限制访问", "下载对话记录", "高级搜索过滤", "优先支持", "保存 Prompt 模板", "代码导出功能"], "button": {"title": "升级到专业版", "url": "#", "variant": "default"}}, {"title": "团队版", "price": "¥199", "period": "/月", "description": "适合开发团队", "features": ["专业版所有功能", "团队协作", "私有项目库", "API 访问", "自定义集成", "专属支持"], "button": {"title": "联系销售", "url": "#"}}]}, "cta": {"name": "cta", "title": "准备好改变您的开发方式了吗？", "description": "加入数千名开发者，一起学习 AI 编程艺术。", "buttons": [{"title": "开始探索", "url": "#", "target": "_self", "icon": "RiRocketLine"}, {"title": "查看文档", "url": "#", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "<PERSON>", "description": "通过真实对话和项目展示 AI 编程艺术。", "logo": {"src": "/logo.png", "alt": "<PERSON>"}, "url": "/"}, "copyright": "© 2025 • Claude Code Show. 保留所有权利。", "nav": {"items": [{"title": "平台", "children": [{"title": "探索项目", "url": "/#explore", "target": "_self"}, {"title": "学习路径", "url": "/#learn", "target": "_self"}, {"title": "定价方案", "url": "/#pricing", "target": "_self"}]}, {"title": "资源", "children": [{"title": "文档", "url": "#", "target": "_blank"}, {"title": "API 参考", "url": "#", "target": "_blank"}, {"title": "博客", "url": "#", "target": "_blank"}]}, {"title": "社区", "children": [{"title": "Discord", "url": "#", "target": "_blank"}, {"title": "GitHub", "url": "#", "target": "_blank"}, {"title": "Twitter", "url": "#", "target": "_blank"}]}]}, "social": {"items": [{"title": "Twitter", "icon": "RiTwitterXFill", "url": "#", "target": "_blank"}, {"title": "GitHub", "icon": "RiGithubFill", "url": "#", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "#", "target": "_blank"}]}, "agreement": {"items": [{"title": "隐私政策", "url": "/privacy-policy"}, {"title": "服务条款", "url": "/terms-of-service"}]}}}