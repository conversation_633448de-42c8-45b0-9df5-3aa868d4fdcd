# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the ShipAny Template One - a Next.js-based AI SaaS boilerplate deployed on Cloudflare Workers. It's a full-stack application with authentication, payments, internationalization, and admin capabilities.

## Tech Stack

- **Framework**: Next.js 15.2.3 with App Router and Turbopack
- **Runtime**: Cloudflare Workers (via OpenNext)
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS v4 + Shadcn UI components
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: NextAuth v5 (beta) with Google, GitHub, and credentials providers
- **Payments**: Stripe integration
- **Internationalization**: next-intl with support for en/zh locales
- **State Management**: React Context API
- **Deployment**: Cloudflare Workers/Pages, Vercel support

## Common Development Commands

```bash
# Development
pnpm dev                    # Start dev server with Turbopack
pnpm lint                   # Run Next.js linting
pnpm build                  # Build for production

# Database Management
pnpm db:generate            # Generate Drizzle migrations
pnpm db:migrate             # Run migrations
pnpm db:studio              # Open Drizzle Studio for DB management
pnpm db:push                # Push schema changes (WARNING: See database note below)

# Cloudflare Deployment
pnpm cf:preview             # Preview on Cloudflare
pnpm cf:deploy              # Deploy to Cloudflare
pnpm cf:upload              # Upload to Cloudflare
pnpm cf:typegen             # Generate Cloudflare types

# Analysis
pnpm analyze                # Bundle analysis
```

## Critical Database Operations Note

**NEVER use `db:push` or any operation that causes data loss.** When schema changes are needed:
1. Generate migration SQL manually
2. Review for any data loss operations
3. Provide SQL statements for manual execution
4. Never force push or accept data loss

## Architecture and Key Directories

### Application Structure
- **`src/app/`**: Next.js App Router pages
  - `[locale]/`: Internationalized routes (en, zh)
  - `(admin)/`: Admin panel routes (protected)
  - `(default)/`: Public and authenticated user routes
  - `(legal)/`: Legal pages (privacy, terms)
  - `api/`: API routes for auth, payments, user operations

### Core Components
- **`src/components/`**:
  - `blocks/`: Reusable layout blocks (header, footer, hero, pricing, etc.)
  - `ui/`: Shadcn UI components
  - `console/`, `dashboard/`: Admin and user dashboard components
  - `sign/`: Authentication components

### Business Logic
- **`src/models/`**: Data models with database operations
- **`src/services/`**: Business logic layer
- **`src/db/`**: Database configuration and schema (Drizzle ORM)

### Configuration
- **`src/auth/`**: NextAuth configuration and handlers
- **`src/i18n/`**: Internationalization setup and translations
- **`src/contexts/`**: React contexts (app state)
- **`src/types/`**: TypeScript type definitions

### AI SDK Integration
- **`src/aisdk/`**: Custom AI SDK for video generation (Kling provider)

## Environment Configuration

Key environment variables (from `.env.example`):
- `DATABASE_URL`: PostgreSQL connection string
- `AUTH_SECRET`: NextAuth secret
- `AUTH_GOOGLE_ID/SECRET`: Google OAuth
- `STRIPE_PUBLIC_KEY/PRIVATE_KEY`: Stripe payment
- `NEXT_PUBLIC_WEB_URL`: Application URL
- `STORAGE_*`: AWS S3-compatible storage config

## Development Guidelines

1. **Component Development**: Use functional components with TypeScript
2. **Styling**: Use Tailwind CSS classes, avoid inline styles
3. **State Management**: Use React Context for global state
4. **Authentication**: All auth flows go through NextAuth
5. **Database Changes**: Never use operations that cause data loss
6. **Internationalization**: Add translations to `src/i18n/messages/`
7. **Type Safety**: Define types in `src/types/` directory

## Deployment Notes

### Cloudflare Deployment
1. Copy `wrangler.toml.example` to `wrangler.toml`
2. Set environment variables in both `.env.production` and `wrangler.toml`
3. Use `pnpm cf:deploy` for deployment

### Database Migrations
Always use safe migration practices:
1. Generate migrations with `pnpm db:generate`
2. Review generated SQL for data loss
3. Apply migrations with `pnpm db:migrate`
4. Never use `db:push` in production

## Key Features

- Multi-tenant SaaS architecture
- User authentication with multiple providers
- Subscription management with Stripe
- Admin dashboard for user/order management
- API key management for external integrations
- Credit system for usage-based billing
- Affiliate/referral system
- Blog/content management
- Responsive design with dark mode support